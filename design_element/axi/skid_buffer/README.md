# Skid Buffer Test Suite

这是一个用于测试 `skid_buffer` SystemVerilog 模块的 cocotb 测试套件。

## 文件说明

- `skid_buffer.sv` - 被测试的 SystemVerilog 模块
- `test_skid_buffer.py` - 包含所有 cocotb 测试函数（使用 @cocotb.test() 装饰器）
- `run_test.py` - pytest 测试运行器，使用 cocotb.runner 创建测试环境
- `requirements.txt` - Python 依赖项

## 安装依赖

```bash
pip install -r requirements.txt
```

确保安装了 Icarus Verilog：
```bash
# Ubuntu/Debian
sudo apt-get install iverilog

# macOS with Homebrew  
brew install icarus-verilog
```

## 运行测试

### 方法1：使用 run_test.py（推荐）
```bash
python run_test.py
```

这会运行所有参数组合的测试（3个数据宽度 × 4个参数组合 = 12个测试）。

### 方法2：直接用 pytest
```bash
pytest run_test.py -v
```

## 为什么要分离文件？

**重要**：`test_skid_buffer.py` 不能直接运行，因为：

1. `@cocotb.test()` 装饰的函数只能在 cocotb 环境中运行
2. `dut` 参数是 cocotb 特有的，pytest 无法直接提供
3. `run_test.py` 使用 `cocotb.runner` 创建正确的环境，然后调用测试模块

## 修复的问题

1. **数据溢出**：添加了 `get_test_data()` 方法，根据 DATA_WIDTH 自动缩放测试数据
2. **asyncio 兼容性**：用 `cocotb.start_soon()` 替代 `asyncio.create_task()`
3. **并发操作**：用 cocotb 的并发机制替代 `asyncio.gather()`

## 测试覆盖

- **基础功能**：复位、透传模式、缓冲模式
- **协议验证**：AXI 握手协议
- **压力测试**：随机握手、连续传输、长时间背压
- **参数测试**：所有 DATA_WIDTH、OPT_LOWPOWER、OPT_REGOUT 组合

## 预期结果

所有测试应该都通过。测试验证：
- 无数据丢失或重复
- 正确的 FIFO 排序
- AXI 协议合规性
- 不同配置下的正确时序 