//==============================================================================
// File Name    : skid_buffer.sv
// Description  : skid buffer
// Author       : jiaqiu
// Email        : <EMAIL>
// Created Date : 2025-07-02
// Modified Date: 2025-07-02
// Version      : 1.0
// Company      : 
// Project      : 
//==============================================================================
// Revision History:
// Date         Version    Author         Description
// 2025-07-02     1.0        jiaqiu         Initial version
//==============================================================================
`timescale 1ns/1ps
module skid_buffer #(
  parameter int DATA_WIDTH = 32,
  parameter bit OPT_LOWPOWER = 0,
  parameter bit OPT_REGOUT = 0
) (
    //-------------------------------------------
    // clk & reset
    //-------------------------------------------
    input  logic  clk_i,
    input  logic  rst_n,
    //-------------------------------------------
    // master
    //-------------------------------------------
    input  logic  valid_i,
    output logic  ready_o,
    input  logic [DATA_WIDTH-1:0] data_i,
    //-------------------------------------------
    // slave
    //-------------------------------------------
    output logic  valid_o,
    input  logic  ready_i,
    output logic [DATA_WIDTH-1:0] data_o
);

  //-------------------------------------------
  // skid buffer registers
  //-------------------------------------------
  logic [DATA_WIDTH-1:0] data_d,data_q;
  logic valid_d,valid_q;

  //**************************** combinational logic ****************************//
  
  // Ready output logic - not ready when buffer is occupied
  assign ready_o = !valid_q;
  always_comb begin
    valid_d = valid_q;
    data_d = data_q;
    if ((valid_i && ready_o) && (valid_o && !ready_i)) begin
      valid_d = 1'b1;
    end else if (ready_i) begin
      valid_d = 1'b0;
    end
    if (OPT_LOWPOWER && (!valid_o || ready_i)) begin
      data_d = {DATA_WIDTH{1'b0}};
    end else if ((!OPT_LOWPOWER||!OPT_REGOUT||valid_i) &&ready_o ) begin
      data_d = data_i;
    end
  end
  //**************************** sequential logic ****************************//
  
  always_ff @(posedge clk_i or negedge rst_n) begin
    if (!rst_n) begin
      valid_q <= 1'b0;
      data_q <= {DATA_WIDTH{1'b0}};
    end else begin
      valid_q <= valid_d;
      data_q <= data_d;
    end
  end

  //**************************** output logic ****************************//
  
  generate
    if (!OPT_REGOUT) begin : NET_OUTPUT
      // Combinatorial outputs
      assign valid_o = valid_i || valid_q;
      
      always_comb begin
        if (valid_q) begin
          data_o = data_q;
        end else if (!OPT_LOWPOWER || valid_i) begin
          data_o = data_i;
        end else begin
          data_o = {DATA_WIDTH{1'b0}};
        end
      end
    
    end else begin : REG_OUTPUT
      // Registered outputs
      logic  ro_valid_d,ro_valid_q;
      logic [DATA_WIDTH-1:0] ro_data_d,ro_data_q;
      always_comb begin
        ro_valid_d = ro_valid_q;
        if (!valid_o || ready_i) begin
          ro_valid_d = (valid_i || valid_q);
        end
      end
      always_ff @(posedge clk_i or negedge rst_n) begin
        if (!rst_n) begin
          ro_valid_q <= 1'b0;
        end else begin
          ro_valid_q <= ro_valid_d;
        end
      end
      
      assign valid_o = ro_valid_q;
      
      always_comb begin
        ro_data_d = ro_data_q;
        if (!valid_o || ready_i) begin
          if (valid_q) begin
            ro_data_d = data_q;
          end else if (!OPT_LOWPOWER || valid_i) begin
            ro_data_d = data_i;
          end else begin
            ro_data_d = {DATA_WIDTH{1'b0}};
          end
        end
      end
      always_ff @(posedge clk_i or negedge rst_n) begin
        if (!rst_n) begin
          ro_data_q <= {DATA_WIDTH{1'b0}};
        end else begin
          ro_data_q <= ro_data_d;
        end
      end
      assign data_o = ro_data_q;
    end
  endgenerate

endmodule //skid_buffer


