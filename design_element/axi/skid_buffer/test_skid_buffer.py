"""
Comprehensive cocotb test for skid_buffer module

This test suite covers:
1. Basic functionality (pass-through, buffering)
2. Handshake protocol verification
3. All parameter combinations (OPT_LOWPOWER, OPT_REGOUT)
4. Stress testing with random delays
5. Edge cases and boundary conditions
6. Data integrity verification
"""

import random
import pytest
import cocotb
from cocotb.clock import Clock
from cocotb.triggers import RisingEdge, Timer, ClockCycles, Combine, First
from cocotb.types import LogicArray
import os
from pathlib import Path

from cocotb.runner import get_runner

class SkidBufferTB:
    """Test bench for skid_buffer"""
    
    def __init__(self, dut):
        self.dut = dut
        self.data_width = int(dut.DATA_WIDTH.value)
        self.max_data = (1 << self.data_width) - 1
        
    def get_test_data(self, value):
        """Get test data that fits in the current data width"""
        return value & self.max_data
        
    async def reset(self):
        """Reset the DUT"""
        self.dut.rst_n.value = 0
        self.dut.valid_i.value = 0
        self.dut.ready_i.value = 0
        self.dut.data_i.value = 0
        await ClockCycles(self.dut.clk_i, 5)
        self.dut.rst_n.value = 1
        await ClockCycles(self.dut.clk_i, 2)
        
    async def send_data(self, data, valid_delay=0):
        """Send data with optional delay"""
        if valid_delay > 0:
            await ClockCycles(self.dut.clk_i, valid_delay)
            
        self.dut.data_i.value = self.get_test_data(data)
        self.dut.valid_i.value = 1
        
        # Wait for ready
        while True:
            await RisingEdge(self.dut.clk_i)
            if self.dut.ready_o.value == 1:
                break
                
        await RisingEdge(self.dut.clk_i)
        self.dut.valid_i.value = 0
        
    async def receive_data(self, ready_delay=0):
        """Receive data with optional delay"""
        if ready_delay > 0:
            await ClockCycles(self.dut.clk_i, ready_delay)
            
        self.dut.ready_i.value = 1
        
        # Wait for valid
        while True:
            await RisingEdge(self.dut.clk_i)
            if self.dut.valid_o.value == 1:
                break
                
        data = int(self.dut.data_o.value)
        await RisingEdge(self.dut.clk_i)
        self.dut.ready_i.value = 0
        
        return data
        
    async def send_and_receive(self, data, send_delay=0, recv_delay=0):
        """Send and receive data simultaneously using cocotb triggers"""
        # Start both operations concurrently using cocotb's Combine
        send_trigger = cocotb.start_soon(self.send_data(data, send_delay))
        receive_trigger = cocotb.start_soon(self.receive_data(recv_delay))
        
        # Wait for both to complete
        await send_trigger
        await receive_trigger
        
        return receive_trigger.result()


@cocotb.test()
async def test_reset_behavior(dut):
    """Test reset behavior"""
    tb = SkidBufferTB(dut)
    
    # Start clock
    cocotb.start_soon(Clock(dut.clk_i, 10, units="ns").start())
    
    # Test reset
    await tb.reset()
    
    # Check initial states
    assert dut.valid_o.value == 0, "valid_o should be 0 after reset"
    assert dut.ready_o.value == 1, "ready_o should be 1 after reset"
    assert dut.data_o.value == 0, "data_o should be 0 after reset"


@cocotb.test()
async def test_pass_through_mode(dut):
    """Test pass-through mode (no backpressure)"""
    tb = SkidBufferTB(dut)
    
    cocotb.start_soon(Clock(dut.clk_i, 10, units="ns").start())
    await tb.reset()
    
    # Test multiple data values (scaled to data width)
    test_values = [0x12, 0xAB, 0xFF, 0x00, 0x55]
    test_data = [tb.get_test_data(val) for val in test_values]
    
    for data in test_data:
        # Send data
        
        dut.data_i.value = data
        dut.valid_i.value = 1
        dut.ready_i.value = 1
        
        await RisingEdge(dut.clk_i)
        # In pass-through mode, data should appear immediately
        # (or next clock for registered output)
        opt_regout = int(dut.OPT_REGOUT.value)
        if not opt_regout:
            # Combinatorial output
            assert dut.valid_o.value == 1, "valid_o should be 1 immediately"
            expected_msg = "Expected 0x{:x}, got 0x{:x}".format(data, int(dut.data_o.value))
            assert int(dut.data_o.value) == data, expected_msg
        else:
            # Registered output - wait one more cycle
            await RisingEdge(dut.clk_i)
            assert dut.valid_o.value == 1, "valid_o should be 1 after clock"
            expected_msg = "Expected 0x{:x}, got 0x{:x}".format(data, int(dut.data_o.value))
            assert int(dut.data_o.value) == data, expected_msg


@cocotb.test()
async def test_buffering_mode(dut):
    """Test buffering when downstream is not ready"""
    tb = SkidBufferTB(dut)
    
    cocotb.start_soon(Clock(dut.clk_i, 10, units="ns").start())
    await tb.reset()
    
    test_data = tb.get_test_data(0xDEAD)  # Scale to data width
    
    # Send first data with downstream not ready
    dut.ready_i.value = 0
    dut.data_i.value = test_data
    dut.valid_i.value = 1
    
    await RisingEdge(dut.clk_i)
    
    # Buffer should accept data, ready_o should still be 1
    assert dut.ready_o.value == 1, "ready_o should still be 1 for first data"
    
    # Send second data - now ready_o should go to 0
    dut.data_i.value = tb.get_test_data(test_data + 1)
    await RisingEdge(dut.clk_i)
    
    assert dut.ready_o.value == 0, "ready_o should be 0 when buffer is full"
    
    # Now make downstream ready
    dut.valid_i.value = 0
    dut.ready_i.value = 1
    
    await RisingEdge(dut.clk_i)
    
    # Check if data comes out correctly
    opt_regout = int(dut.OPT_REGOUT.value)
    if not opt_regout:
        assert dut.valid_o.value == 1, "valid_o should be 1"
        expected_msg = "Expected 0x{:x}, got 0x{:x}".format(test_data, int(dut.data_o.value))
        assert int(dut.data_o.value) == test_data, expected_msg

    else:
        await RisingEdge(dut.clk_i)
        assert dut.valid_o.value == 1, "valid_o should be 1"
        expected_msg = "Expected 0x{:x}, got 0x{:x}".format(test_data, int(dut.data_o.value))
        assert int(dut.data_o.value) == test_data, expected_msg


@cocotb.test()
async def test_handshake_protocol(dut):
    """Test AXI-style handshake protocol"""
    tb = SkidBufferTB(dut)
    
    cocotb.start_soon(Clock(dut.clk_i, 10, units="ns").start())
    await tb.reset()
    
    test_data = tb.get_test_data(0xCAFE)  # Scale to data width
    
    # Test: valid before ready
    dut.data_i.value = test_data
    dut.valid_i.value = 1
    dut.ready_i.value = 0
    
    await RisingEdge(dut.clk_i)
    
    # Data should be stable while valid && !ready
    for _ in range(5):
        assert dut.valid_i.value == 1, "valid_i should remain stable"
        assert int(dut.data_i.value) == test_data, "data_i should remain stable"
        await RisingEdge(dut.clk_i)
    
    # Now assert ready
    dut.ready_i.value = 1
    await RisingEdge(dut.clk_i)
    
    # Clear valid
    dut.valid_i.value = 0
    dut.ready_i.value = 0
    await RisingEdge(dut.clk_i)


@cocotb.test()
async def test_data_integrity(dut):
    """Test data integrity - no loss, no duplication"""
    tb = SkidBufferTB(dut)
    
    cocotb.start_soon(Clock(dut.clk_i, 10, units="ns").start())
    await tb.reset()
    
    # Generate test sequence (scaled to data width)
    test_sequence = [tb.get_test_data(i) for i in range(20)]
    received_data = []
    
    async def sender():
        for data in test_sequence:
            delay = random.randint(0, 2)
            await tb.send_data(data, delay)
    
    async def receiver():
        for _ in test_sequence:
            delay = random.randint(0, 2)
            data = await tb.receive_data(delay)
            received_data.append(data)
    
    # Run sender and receiver concurrently using cocotb
    sender_task = cocotb.start_soon(sender())
    receiver_task = cocotb.start_soon(receiver())
    
    await sender_task
    await receiver_task
    
    # Verify data integrity
    error_msg = "Data mismatch: expected {}, got {}".format(test_sequence, received_data)
    assert received_data == test_sequence, error_msg


@cocotb.test()
async def test_random_handshake(dut):
    """Test with random valid/ready patterns"""
    tb = SkidBufferTB(dut)
    
    cocotb.start_soon(Clock(dut.clk_i, 10, units="ns").start())
    await tb.reset()
    
    sent_data = []
    received_data = []
    
    max_cycles = 400  # safety timeout

    finished = cocotb.triggers.Event()

    async def random_sender():
        for _ in range(15):
            if random.random() > 0.3:  # 70% chance to attempt send
                data = random.randint(0, min(255, tb.max_data))
                dut.data_i.value = data
                dut.valid_i.value = 1

                # Wait until handshake completes (valid & ready)
                while True:
                    await RisingEdge(dut.clk_i)
                    if dut.ready_o.value == 1:
                        break

                # Data accepted this cycle
                sent_data.append(data)

                await RisingEdge(dut.clk_i)  # De-assert after acceptance
                dut.valid_i.value = 0

                # Optional random idle cycles
                if random.random() > 0.5:
                    await ClockCycles(dut.clk_i, random.randint(1, 2))
            else:
                await RisingEdge(dut.clk_i)
        
        finished.set()  # Signal sender completion

    async def random_receiver():
        cycles = 0
        while cycles < max_cycles:
            # Exit when sender done and all data received
            if finished.is_set() and len(received_data) >= len(sent_data):
                break
            if random.random() > 0.3:  # 70% chance to be ready
                dut.ready_i.value = 1
                await RisingEdge(dut.clk_i)
                
                if dut.valid_o.value == 1:
                    received_data.append(int(dut.data_o.value))
                
                dut.ready_i.value = 0
                
                # Random delay
                if random.random() > 0.5:
                    await ClockCycles(dut.clk_i, random.randint(1, 2))
            else:
                dut.ready_i.value = 0
                await RisingEdge(dut.clk_i)
            
            cycles += 1

    # Start both tasks using cocotb
    sender_task = cocotb.start_soon(random_sender())
    receiver_task = cocotb.start_soon(random_receiver())
    
    await sender_task
    await receiver_task
    
    # Verify data integrity
    error_msg = "Mismatch between sent and received data in random handshake test"
    assert received_data == sent_data, error_msg


@cocotb.test()
async def test_continuous_transfer(dut):
    """Test 100% throughput continuous transfer"""
    tb = SkidBufferTB(dut)
    
    cocotb.start_soon(Clock(dut.clk_i, 10, units="ns").start())
    await tb.reset()
    
    # Test continuous transfer (scaled data)
    test_data = [tb.get_test_data(i) for i in range(10)]
    received_data = []
    
    # Set both valid and ready continuously
    dut.valid_i.value = 1
    dut.ready_i.value = 1
    
    for i, data in enumerate(test_data):
        dut.data_i.value = data
        await RisingEdge(dut.clk_i)
        
        # Check output based on configuration
        opt_regout = int(dut.OPT_REGOUT.value)
        if not opt_regout and i > 0:  # Combinatorial output (skip first)
            if dut.valid_o.value == 1:
                received_data.append(int(dut.data_o.value))
        elif opt_regout and i > 1:  # Registered output (skip first two)
            if dut.valid_o.value == 1:
                received_data.append(int(dut.data_o.value))
    
    # Clean up
    dut.valid_i.value = 0
    
    # Wait for remaining data
    for _ in range(5):
        await RisingEdge(dut.clk_i)
        if dut.valid_o.value == 1:
            received_data.append(int(dut.data_o.value))
    
    dut.ready_i.value = 0
    
    # Verify we got the expected data (accounting for pipeline delays)
    min_expected = len(test_data) - 2
    error_msg = "Expected at least {} items, got {}".format(min_expected, len(received_data))
    assert len(received_data) >= min_expected, error_msg


@cocotb.test()
async def test_opt_lowpower_behavior(dut):
    """Test OPT_LOWPOWER specific behavior"""
    tb = SkidBufferTB(dut)
    
    cocotb.start_soon(Clock(dut.clk_i, 10, units="ns").start())
    await tb.reset()
    
    # Test normal operation works
    test_data = tb.get_test_data(0x1234)
    result = await tb.send_and_receive(test_data)
    assert result == test_data, "Low power mode affected normal operation"


@cocotb.test()
async def test_single_cycle_transfer(dut):
    """Test single clock cycle transfers"""
    tb = SkidBufferTB(dut)
    
    cocotb.start_soon(Clock(dut.clk_i, 10, units="ns").start())
    await tb.reset()
    
    test_data = tb.get_test_data(0x5A5A)
    
    # Single cycle transfer
    dut.data_i.value = test_data
    dut.valid_i.value = 1
    dut.ready_i.value = 1
    
    await RisingEdge(dut.clk_i)
    
    # Clear signals
    dut.valid_i.value = 0
    dut.ready_i.value = 0
    
    await ClockCycles(dut.clk_i, 2)


@cocotb.test()
async def test_long_backpressure(dut):
    """Test long periods of backpressure"""
    tb = SkidBufferTB(dut)
    
    cocotb.start_soon(Clock(dut.clk_i, 10, units="ns").start())
    await tb.reset()
    
    test_data = tb.get_test_data(0xBEEF)
    
    # Send data but don't assert ready for a long time
    dut.data_i.value = test_data
    dut.valid_i.value = 1
    dut.ready_i.value = 0
    
    await RisingEdge(dut.clk_i)
    
    # Send second data to fill buffer
    dut.data_i.value = tb.get_test_data(test_data + 1)
    await RisingEdge(dut.clk_i)
    
    # Buffer should be full, ready_o should be 0
    assert dut.ready_o.value == 0, "ready_o should be 0 when buffer is full"
    
    # Keep data stable for many cycles
    for _ in range(10):
        assert dut.ready_o.value == 0, "ready_o should remain 0"
        await RisingEdge(dut.clk_i)
    
    # Finally assert ready
    dut.valid_i.value = 0
    dut.ready_i.value = 1
    
    await RisingEdge(dut.clk_i)
    
    # Wait until valid_o is asserted
    for _ in range(5):
        if dut.valid_o.value == 1:
            break
        await RisingEdge(dut.clk_i)
    else:
        assert False, "Buffered data did not appear after releasing back-pressure"

    assert int(dut.data_o.value) == test_data, "First buffered data mismatch"
    # Keep ready asserted to fetch remaining buffered data
    dut.ready_i.value = 1

    # Wait for second data
    expected_second = tb.get_test_data(test_data + 1)
    for _ in range(5):
        await RisingEdge(dut.clk_i)
        if dut.valid_o.value == 1 and int(dut.data_o.value) == expected_second:
            break
    else:
        assert False, "Second buffered data did not appear"

    assert int(dut.data_o.value) == expected_second, "Second buffered data mismatch"

    # De-assert ready to conclude
    dut.ready_i.value = 0


# Parameterized tests for different configurations
@pytest.mark.parametrize("data_width", [8, 16, 32])
@pytest.mark.parametrize("opt_lowpower", [0, 1])
@pytest.mark.parametrize("opt_regout", [0, 1])
def test_skid_buffer_configurations(data_width, opt_lowpower, opt_regout):
    """Test all parameter combinations"""
    
    """Internal test function"""
    sim = os.getenv("SIM", "icarus")
    
    proj_path = Path(__file__).resolve().parent
    
    # Get the runner
    runner = get_runner(sim)
    build_str = "sim_build/skid_buffer_" + f"W={data_width}O={opt_lowpower}R={opt_regout}"

    # Add sources
    runner.build(
        verilog_sources=[proj_path / "skid_buffer.sv"],
        hdl_toplevel="skid_buffer",
        always=True,
        parameters={
            "DATA_WIDTH": data_width,
            "OPT_LOWPOWER": opt_lowpower,
            "OPT_REGOUT": opt_regout,
        },
        waves=True,
        build_dir=build_str
    )
    
    # Run the test
    runner.test(
        hdl_toplevel="skid_buffer",
        test_module="test_skid_buffer"
    )
    


if __name__ == "__main__":
    # Default test run
    pytest.main(["-xvs", __file__])