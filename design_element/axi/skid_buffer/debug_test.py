"""
Debug test to understand skid buffer behavior
"""

import cocotb
from cocotb.clock import Clock
from cocotb.triggers import RisingEdge, ClockCycles

@cocotb.test()
async def debug_skid_buffer(dut):
    """Debug the skid buffer behavior step by step"""
    
    # Start clock
    cocotb.start_soon(Clock(dut.clk_i, 10, units="ns").start())
    
    # Reset
    dut.rst_n.value = 0
    dut.valid_i.value = 0
    dut.ready_i.value = 0
    dut.data_i.value = 0
    await ClockCycles(dut.clk_i, 5)
    dut.rst_n.value = 1
    await ClockCycles(dut.clk_i, 2)
    
    print("=== After Reset ===")
    print(f"valid_o={dut.valid_o.value}, ready_o={dut.ready_o.value}, data_o=0x{int(dut.data_o.value):x}")
    print(f"valid_q={dut.valid_q.value}, data_q=0x{int(dut.data_q.value):x}")
    
    # Step 1: Send first data, downstream not ready
    print("\n=== Step 1: Send first data, downstream not ready ===")
    dut.data_i.value = 0xAAA
    dut.valid_i.value = 1
    dut.ready_i.value = 0
    
    await RisingEdge(dut.clk_i)
    print(f"valid_o={dut.valid_o.value}, ready_o={dut.ready_o.value}, data_o=0x{int(dut.data_o.value):x}")
    print(f"valid_q={dut.valid_q.value}, data_q=0x{int(dut.data_q.value):x}")
    
    # Step 2: Send second data while first is stuck
    print("\n=== Step 2: Send second data while first is stuck ===")
    dut.data_i.value = 0xBBB
    # Keep valid_i=1, ready_i=0
    
    await RisingEdge(dut.clk_i)
    print(f"valid_o={dut.valid_o.value}, ready_o={dut.ready_o.value}, data_o=0x{int(dut.data_o.value):x}")
    print(f"valid_q={dut.valid_q.value}, data_q=0x{int(dut.data_q.value):x}")
    
    # Step 3: Keep sending data for a few cycles
    print("\n=== Step 3: Keep sending data for a few cycles ===")
    for i in range(3):
        await RisingEdge(dut.clk_i)
        print(f"Cycle {i}: valid_o={dut.valid_o.value}, ready_o={dut.ready_o.value}, data_o=0x{int(dut.data_o.value):x}")
        print(f"         valid_q={dut.valid_q.value}, data_q=0x{int(dut.data_q.value):x}")
    
    # Step 4: Make downstream ready
    print("\n=== Step 4: Make downstream ready ===")
    dut.valid_i.value = 0
    dut.ready_i.value = 1
    
    await RisingEdge(dut.clk_i)
    print(f"valid_o={dut.valid_o.value}, ready_o={dut.ready_o.value}, data_o=0x{int(dut.data_o.value):x}")
    print(f"valid_q={dut.valid_q.value}, data_q=0x{int(dut.data_q.value):x}")
    
    # Step 5: Wait a few more cycles
    print("\n=== Step 5: Wait a few more cycles ===")
    for i in range(3):
        await RisingEdge(dut.clk_i)
        print(f"Cycle {i}: valid_o={dut.valid_o.value}, ready_o={dut.ready_o.value}, data_o=0x{int(dut.data_o.value):x}")
        print(f"         valid_q={dut.valid_q.value}, data_q=0x{int(dut.data_q.value):x}")
    
    dut.ready_i.value = 0
