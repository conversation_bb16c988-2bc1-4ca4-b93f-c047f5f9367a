"""
Test runner for skid_buffer module using cocotb.runner

This file properly sets up the cocotb simulation environment and runs
the tests defined in test_skid_buffer.py with different parameter combinations.
"""

import pytest
import os
import sys
from pathlib import Path
from itertools import product

# Import cocotb runner
from cocotb.runner import get_runner

# Define parameter combinations to test
DATA_WIDTH_VALUES = [8, 16, 32]
OPT_LOWPOWER_VALUES = [0, 1]
OPT_REGOUT_VALUES = [0, 1]

def idfn(val):
    """Generate test IDs for pytest parametrization"""
    if isinstance(val, list):
        return '-'.join(str(x) for x in val)
    else:
        return str(val)

@pytest.mark.parametrize("data_width", DATA_WIDTH_VALUES, ids=idfn)
@pytest.mark.parametrize("opt_lowpower", OPT_LOWPOWER_VALUES, ids=idfn)
@pytest.mark.parametrize("opt_regout", OPT_REGOUT_VALUES, ids=idfn)
def test_skid_buffer_configurations(data_width, opt_lowpower, opt_regout):
    """Test all parameter combinations using cocotb.runner"""
    
    # Get current file directory
    tests_dir = Path(__file__).parent
    hdl_dir = tests_dir
    sim = os.getenv("SIM", "icarus")
    
    # Define parameters
    parameters = {
        "DATA_WIDTH": data_width,
        "OPT_LOWPOWER": opt_lowpower,
        "OPT_REGOUT": opt_regout,
    }
    
    # Create unique build directory for this configuration
    build_str = f"sim_build/skid_buffer_W={data_width}O={opt_lowpower}R={opt_regout}"
    
    # Use cocotb runner to run tests
    runner = get_runner(sim)
    
    # Build the simulation
    runner.build(
        verilog_sources=[hdl_dir / "skid_buffer.sv"],
        hdl_toplevel="skid_buffer",
        parameters=parameters,
        waves=True,
        build_dir=build_str,
        always=True  # Always rebuild to ensure clean state
    )
    
    # Run the tests
    runner.test(
        test_module="test_skid_buffer",
        hdl_toplevel="skid_buffer",
        parameters=parameters,
        extra_env={}
    )

def test_single_configuration():
    """Run tests with default configuration (for quick testing)"""
    test_skid_buffer_configurations(32, 0, 0)

def test_debug():
    """Run debug test to understand skid buffer behavior"""
    tests_dir = Path(__file__).parent
    hdl_dir = tests_dir
    sim = os.getenv("SIM", "icarus")

    parameters = {
        "DATA_WIDTH": 32,
        "OPT_LOWPOWER": 0,
        "OPT_REGOUT": 0,
    }

    build_str = f"sim_build/skid_buffer_debug"

    runner = get_runner(sim)

    runner.build(
        verilog_sources=[hdl_dir / "skid_buffer.sv"],
        hdl_toplevel="skid_buffer",
        parameters=parameters,
        waves=True,
        build_dir=build_str,
        always=True
    )

    runner.test(
        test_module="debug_test",
        hdl_toplevel="skid_buffer",
        parameters=parameters,
        extra_env={},
        testcase="debug_skid_buffer"
    )

if __name__ == "__main__":
    # Run with default configuration if called directly
    if len(sys.argv) == 1:
        print("Running tests with default configuration (DATA_WIDTH=32, OPT_LOWPOWER=0, OPT_REGOUT=0)")
        test_single_configuration()
    else:
        # Run with pytest for all configurations
        pytest.main(["-xvs", __file__])
