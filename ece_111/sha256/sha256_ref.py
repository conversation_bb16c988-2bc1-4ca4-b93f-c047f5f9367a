#!/usr/bin/env python3
"""
SHA-256 算法的完整实现
基于 FIPS PUB 180-4 标准

作者：参考文档实现
日期：2025年
"""

import struct
import hashlib


class SHA256:
    """SHA-256 哈希算法实现"""

    def __init__(self):
        # 初始哈希值 H^(0) - 前8个素数的平方根的小数部分前32位
        self.h = [
            0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a,
            0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19
        ]

        # 轮常数 K_t - 前64个素数的立方根的小数部分前32位
        self.k = [
            0x428a2f98, 0x71374491, 0xb5c0fbcf, 0xe9b5dba5,
            0x3956c25b, 0x59f111f1, 0x923f82a4, 0xab1c5ed5,
            0xd807aa98, 0x12835b01, 0x243185be, 0x550c7dc3,
            0x72be5d74, 0x80deb1fe, 0x9bdc06a7, 0xc19bf174,
            0xe49b69c1, 0xefbe4786, 0x0fc19dc6, 0x240ca1cc,
            0x2de92c6f, 0x4a7484aa, 0x5cb0a9dc, 0x76f988da,
            0x983e5152, 0xa831c66d, 0xb00327c8, 0xbf597fc7,
            0xc6e00bf3, 0xd5a79147, 0x06ca6351, 0x14292967,
            0x27b70a85, 0x2e1b2138, 0x4d2c6dfc, 0x53380d13,
            0x650a7354, 0x766a0abb, 0x81c2c92e, 0x92722c85,
            0xa2bfe8a1, 0xa81a664b, 0xc24b8b70, 0xc76c51a3,
            0xd192e819, 0xd6990624, 0xf40e3585, 0x106aa070,
            0x19a4c116, 0x1e376c08, 0x2748774c, 0x34b0bcb5,
            0x391c0cb3, 0x4ed8aa4a, 0x5b9cca4f, 0x682e6ff3,
            0x748f82ee, 0x78a5636f, 0x84c87814, 0x8cc70208,
            0x90befffa, 0xa4506ceb, 0xbef9a3f7, 0xc67178f2
        ]

    def _rotr(self, x, n):
        """32位循环右移"""
        return ((x >> n) | (x << (32 - n))) & 0xffffffff

    def _shr(self, x, n):
        """32位右移"""
        return x >> n

    def _ch(self, x, y, z):
        """Choose函数: (x & y) ^ (~x & z)"""
        return (x & y) ^ (~x & z)

    def _maj(self, x, y, z):
        """Majority函数: (x & y) ^ (x & z) ^ (y & z)"""
        return (x & y) ^ (x & z) ^ (y & z)

    def _sigma0(self, x):
        """Σ0函数"""
        return self._rotr(x, 2) ^ self._rotr(x, 13) ^ self._rotr(x, 22)

    def _sigma1(self, x):
        """Σ1函数"""
        return self._rotr(x, 6) ^ self._rotr(x, 11) ^ self._rotr(x, 25)

    def _gamma0(self, x):
        """σ0函数 (小写sigma)"""
        return self._rotr(x, 7) ^ self._rotr(x, 18) ^ self._shr(x, 3)

    def _gamma1(self, x):
        """σ1函数 (小写sigma)"""
        return self._rotr(x, 17) ^ self._rotr(x, 19) ^ self._shr(x, 10)

    def _preprocess(self, message):
        """消息预处理：填充和分块"""
        if isinstance(message, str):
            message = message.encode('utf-8')

        # 原始消息长度（位）
        original_len_bits = len(message) * 8

        # 1. 附加 '1' 位 (0x80)
        message += b'\x80'

        # 2. 附加 '0' 位，直到长度 ≡ 448 (mod 512)
        while (len(message) * 8) % 512 != 448:
            message += b'\x00'

        # 3. 附加64位大端序的原始长度
        message += struct.pack('>Q', original_len_bits)

        # 4. 分割为512位（64字节）的块
        chunks = []
        for i in range(0, len(message), 64):
            chunks.append(message[i:i+64])

        return chunks

    def _process_chunk(self, chunk):
        """处理单个512位消息块"""
        # 消息调度：扩展为64个32位字
        w = [0] * 64

        # 前16个字直接来自消息块（大端序）``
        for i in range(16):
            w[i] = struct.unpack('>I', chunk[i*4:(i+1)*4])[0]

        # 后48个字通过公式生成
        for i in range(16, 64):
            s0 = self._gamma0(w[i-15])
            s1 = self._gamma1(w[i-2])
            w[i] = (w[i-16] + s0 + w[i-7] + s1) & 0xffffffff

        # 初始化工作变量
        a, b, c, d, e, f, g, h = self.h

        # 64轮主循环
        for i in range(64):
            S1 = self._sigma1(e)
            ch = self._ch(e, f, g)
            temp1 = (h + S1 + ch + self.k[i] + w[i]) & 0xffffffff

            S0 = self._sigma0(a)
            maj = self._maj(a, b, c)
            temp2 = (S0 + maj) & 0xffffffff

            h = g
            g = f
            f = e
            e = (d + temp1) & 0xffffffff
            d = c
            c = b
            b = a
            a = (temp1 + temp2) & 0xffffffff

        # 更新哈希值
        self.h[0] = (self.h[0] + a) & 0xffffffff
        self.h[1] = (self.h[1] + b) & 0xffffffff
        self.h[2] = (self.h[2] + c) & 0xffffffff
        self.h[3] = (self.h[3] + d) & 0xffffffff
        self.h[4] = (self.h[4] + e) & 0xffffffff
        self.h[5] = (self.h[5] + f) & 0xffffffff
        self.h[6] = (self.h[6] + g) & 0xffffffff
        self.h[7] = (self.h[7] + h) & 0xffffffff

    def digest(self, message):
        """计算SHA-256哈希值"""
        # 重置状态
        self.h = [
            0x6a09e667, 0xbb67ae85, 0x3c6ef372, 0xa54ff53a,
            0x510e527f, 0x9b05688c, 0x1f83d9ab, 0x5be0cd19
        ]

        # 预处理消息
        chunks = self._preprocess(message)

        # 处理每个块
        for chunk in chunks:
            self._process_chunk(chunk)

        # 生成最终摘要
        digest = b''
        for h_val in self.h:
            digest += struct.pack('>I', h_val)

        return digest

    def hexdigest(self, message):
        """返回十六进制格式的哈希值"""
        return self.digest(message).hex()


def sha256_custom(message):
    """便捷函数：计算SHA-256哈希值"""
    sha = SHA256()
    return sha.hexdigest(message)


def test_sha256():
    """测试SHA-256实现的正确性"""
    print("SHA-256 算法实现测试")
    print("=" * 50)

    # 测试用例
    test_cases = [
        "",  # 空字符串
        "a",  # 单字符
        "abc",  # 标准测试向量
        "message digest",  # 中等长度
        "abcdefghijklmnopqrstuvwxyz",  # 字母表
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",  # 长字符串
        "1234567890" * 8,  # 80字符
        "a" * 1000000,  # 百万个'a'（测试长消息）
    ]

    sha_custom = SHA256()

    for i, test_input in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: ", end="")
        if len(test_input) > 50:
            print(f"'{test_input[:20]}...' (长度: {len(test_input)})")
        else:
            print(f"'{test_input}'")

        # 自定义实现
        custom_result = sha_custom.hexdigest(test_input)

        # Python标准库实现
        standard_result = hashlib.sha256(test_input.encode('utf-8')).hexdigest()

        print(f"自定义实现: {custom_result}")
        print(f"标准库实现: {standard_result}")
        print(f"结果匹配: {'✓' if custom_result == standard_result else '✗'}")

        if custom_result != standard_result:
            print("❌ 测试失败！")
            return False

    print("\n" + "=" * 50)
    print("🎉 所有测试通过！SHA-256实现正确。")
    return True


def prepare_hardware_message(message, target_words=20):
    """
    Prepare a message for hardware SHA-256 implementation
    Returns exactly target_words (default 20) 32-bit words
    """
    if isinstance(message, str):
        message = message.encode('utf-8')

    # Original message length in bits
    original_len_bits = len(message) * 8

    # Add padding bit (0x80)
    message += b'\x80'

    # Add zero padding to make it 448 bits (56 bytes) mod 512
    while (len(message) * 8) % 512 != 448:
        message += b'\x00'

    # Add length as 64-bit big-endian
    message += struct.pack('>Q', original_len_bits)

    # Convert to 32-bit words (big-endian)
    words = []
    for i in range(0, len(message), 4):
        if i + 4 <= len(message):
            word = struct.unpack('>I', message[i:i+4])[0]
        else:
            # Handle partial word at the end
            partial = message[i:] + b'\x00' * (4 - len(message[i:]))
            word = struct.unpack('>I', partial)[0]
        words.append(word)

    # Ensure exactly target_words
    while len(words) < target_words:
        words.append(0)

    return words[:target_words]


def write_memory_file(words, filename):
    """
    Write words to a memory initialization file
    """
    with open(filename, 'w') as f:
        for i, word in enumerate(words):
            f.write(f"@{i:04x} {word:08x}\n")


def verify_hardware_result(message, result_words):
    """
    Verify hardware result against reference implementation
    """
    # Compute reference
    sha = SHA256()
    reference_hash = sha.digest(message)

    # Convert result words to bytes
    result_bytes = b''.join(struct.pack('>I', word) for word in result_words)

    return result_bytes == reference_hash


def test_hardware_integration():
    """
    Test the hardware integration functions
    """
    print("Testing hardware integration functions")
    print("=" * 50)

    test_messages = ["", "a", "abc", "hello world", "The quick brown fox jumps over the lazy dog"]

    for message in test_messages:
        print(f"\nTesting message: '{message}'")

        # Prepare for hardware
        hw_words = prepare_hardware_message(message)
        print(f"Hardware words ({len(hw_words)}): {[hex(w) for w in hw_words[:8]]}...")

        # Compute reference
        sha = SHA256()
        ref_hash = sha.digest(message)
        ref_words = [struct.unpack('>I', ref_hash[i:i+4])[0] for i in range(0, len(ref_hash), 4)]

        print(f"Reference hash: {ref_hash.hex()}")
        print(f"Reference words: {[hex(w) for w in ref_words]}")

        # Verify
        is_valid = verify_hardware_result(message, ref_words)
        print(f"Verification: {'✓' if is_valid else '✗'}")

    print("\n" + "=" * 50)
    print("Hardware integration test completed")


if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "hardware":
        test_hardware_integration()
    else:
        test_sha256()