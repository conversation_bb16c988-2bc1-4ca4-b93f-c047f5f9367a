`timescale 1ns / 1ps

module sha256_top #(
    parameter ADDR_WIDTH = 16,
    parameter DATA_WIDTH = 32
) (
    input  logic                  clk,
    input  logic                  rst_n,
    input  logic                  start,
    input  logic [ADDR_WIDTH-1:0] message_addr,
    input  logic [ADDR_WIDTH-1:0] output_addr,
    output logic                  done
);

  // Internal memory interface signals
  logic                  mem_clk;
  logic                  mem_we;
  logic [ADDR_WIDTH-1:0] mem_addr;
  logic [DATA_WIDTH-1:0] mem_write_data;
  logic [DATA_WIDTH-1:0] mem_read_data;

  // Instantiate SHA-256 module
  sha256 sha256_inst (
      .clk           (clk),
      .rst_n         (rst_n),
      .start         (start),
      .message_addr  (message_addr),
      .output_addr   (output_addr),
      .done          (done),
      .mem_clk       (mem_clk),
      .mem_we        (mem_we),
      .mem_addr      (mem_addr),
      .mem_write_data(mem_write_data),
      .mem_read_data (mem_read_data)
  );

  // Instantiate memory module
  mem #(
      .ADDR_WIDTH(ADDR_WIDTH),
      .DATA_WIDTH(DATA_WIDTH)
  ) mem_inst (
      .clk      (mem_clk),
      .mem_addr (mem_addr),
      .mem_wdata(mem_write_data),
      .wr_rd    (mem_we),
      .mem_rdata(mem_read_data)
  );

endmodule
