`timescale 1ns / 1ps
/* verilator lint_off WIDTHTRUNC */
/* verilator lint_off WIDTHEXPAND */
module sha256 (
    input  logic        clk,
    input  logic        rst_n,
    input  logic        start,
    input  logic [15:0] message_addr,
    input  logic [15:0] output_addr,
    output logic        done,
    output logic        mem_clk,
    output logic        mem_we,
    output logic [15:0] mem_addr,
    output logic [31:0] mem_write_data,
    input  logic [31:0] mem_read_data
);

  // SHA-256 initial hash values
  logic [31:0] H_INIT[0:7];
  initial begin
    H_INIT[0] = 32'h6a09e667;
    H_INIT[1] = 32'hbb67ae85;
    H_INIT[2] = 32'h3c6ef372;
    H_INIT[3] = 32'ha54ff53a;
    H_INIT[4] = 32'h510e527f;
    H_INIT[5] = 32'h9b05688c;
    H_INIT[6] = 32'h1f83d9ab;
    H_INIT[7] = 32'h5be0cd19;
  end

  // SHA-256 round constants
  logic [31:0] K[0:63];
  initial begin
    K[0]  = 32'h428a2f98;
    K[1]  = 32'h71374491;
    K[2]  = 32'hb5c0fbcf;
    K[3]  = 32'he9b5dba5;
    K[4]  = 32'h3956c25b;
    K[5]  = 32'h59f111f1;
    K[6]  = 32'h923f82a4;
    K[7]  = 32'hab1c5ed5;
    K[8]  = 32'hd807aa98;
    K[9]  = 32'h12835b01;
    K[10] = 32'h243185be;
    K[11] = 32'h550c7dc3;
    K[12] = 32'h72be5d74;
    K[13] = 32'h80deb1fe;
    K[14] = 32'h9bdc06a7;
    K[15] = 32'hc19bf174;
    K[16] = 32'he49b69c1;
    K[17] = 32'hefbe4786;
    K[18] = 32'h0fc19dc6;
    K[19] = 32'h240ca1cc;
    K[20] = 32'h2de92c6f;
    K[21] = 32'h4a7484aa;
    K[22] = 32'h5cb0a9dc;
    K[23] = 32'h76f988da;
    K[24] = 32'h983e5152;
    K[25] = 32'ha831c66d;
    K[26] = 32'hb00327c8;
    K[27] = 32'hbf597fc7;
    K[28] = 32'hc6e00bf3;
    K[29] = 32'hd5a79147;
    K[30] = 32'h06ca6351;
    K[31] = 32'h14292967;
    K[32] = 32'h27b70a85;
    K[33] = 32'h2e1b2138;
    K[34] = 32'h4d2c6dfc;
    K[35] = 32'h53380d13;
    K[36] = 32'h650a7354;
    K[37] = 32'h766a0abb;
    K[38] = 32'h81c2c92e;
    K[39] = 32'h92722c85;
    K[40] = 32'ha2bfe8a1;
    K[41] = 32'ha81a664b;
    K[42] = 32'hc24b8b70;
    K[43] = 32'hc76c51a3;
    K[44] = 32'hd192e819;
    K[45] = 32'hd6990624;
    K[46] = 32'hf40e3585;
    K[47] = 32'h106aa070;
    K[48] = 32'h19a4c116;
    K[49] = 32'h1e376c08;
    K[50] = 32'h2748774c;
    K[51] = 32'h34b0bcb5;
    K[52] = 32'h391c0cb3;
    K[53] = 32'h4ed8aa4a;
    K[54] = 32'h5b9cca4f;
    K[55] = 32'h682e6ff3;
    K[56] = 32'h748f82ee;
    K[57] = 32'h78a5636f;
    K[58] = 32'h84c87814;
    K[59] = 32'h8cc70208;
    K[60] = 32'h90befffa;
    K[61] = 32'ha4506ceb;
    K[62] = 32'hbef9a3f7;
    K[63] = 32'hc67178f2;
  end

  // SHA-256 functions
  function logic [31:0] rotr(input logic [31:0] x, input int n);
    rotr = (x >> n) | (x << (32 - n));
  endfunction

  function logic [31:0] shr(input logic [31:0] x, input int n);
    shr = x >> n;
  endfunction

  function logic [31:0] ch(input logic [31:0] x, input logic [31:0] y, input logic [31:0] z);
    ch = (x & y) ^ (~x & z);
  endfunction

  function logic [31:0] maj(input logic [31:0] x, input logic [31:0] y, input logic [31:0] z);
    maj = (x & y) ^ (x & z) ^ (y & z);
  endfunction

  function logic [31:0] sigma0(input logic [31:0] x);
    sigma0 = rotr(x, 2) ^ rotr(x, 13) ^ rotr(x, 22);
  endfunction

  function logic [31:0] sigma1(input logic [31:0] x);
    sigma1 = rotr(x, 6) ^ rotr(x, 11) ^ rotr(x, 25);
  endfunction

  function logic [31:0] gamma0(input logic [31:0] x);
    gamma0 = rotr(x, 7) ^ rotr(x, 18) ^ shr(x, 3);
  endfunction

  function logic [31:0] gamma1(input logic [31:0] x);
    gamma1 = rotr(x, 17) ^ rotr(x, 19) ^ shr(x, 10);
  endfunction


  // State machine states
  typedef enum logic [3:0] {
    IDLE,
    GENERATE_WORDS,
    COMPRESS,
    UPDATE_HASH,
    WRITE_OUTPUT,
    DONE
  } state_t;
  // Internal registers
  state_t cur_state, next_state;
  logic [31:0] w   [0:63];  // Message schedule
  logic [31:0] hash[ 0:7];  // Current hash values
  logic [31:0] a, b, c, d, e, f, g, h;  // Working variables
  logic [6:0]
      word_index, word_index_next;  // Index for reading/writing words (needs to count to 64)
  logic [5:0] round_index, round_index_next;  // Index for hash rounds

  logic [31:0] temp1_comb, temp2_comb;  // Combinational temporary variables
  logic [31:0] chunk_idx, chunk_idx_next;
  logic is_last_chunk;  // Flag to indicate if this is the last chunk
  localparam MAX_CHUNKS = 32;  // Maximum supported chunks
  // Memory interface
  assign mem_clk = clk;

  // Combinational computation of temporary values for hash rounds
  always_comb begin
    temp1_comb = h + sigma1(e) + ch(e, f, g) + K[round_index] + w[round_index];
    temp2_comb = sigma0(a) + maj(a, b, c);
  end

  // Combinational logic for next state and outputs
  always_comb begin
    next_state       = cur_state;
    mem_we           = 1'b0;
    mem_addr         = 16'h0;
    mem_write_data   = 32'h0;
    done             = 1'b0;
    word_index_next  = word_index;
    round_index_next = round_index;
    chunk_idx_next   = chunk_idx;
    case (cur_state)
      IDLE: begin
        word_index_next  = 7'd0;
        round_index_next = 6'd0;
        chunk_idx_next   = 0;
        if (start) begin
          next_state = GENERATE_WORDS;
        end
        mem_addr = message_addr;
      end

      GENERATE_WORDS: begin
        word_index_next = word_index + 1;
        mem_addr        = message_addr + {9'h0, word_index_next} + chunk_idx * 16;
        if (word_index == 7'd64 - 1) begin
          next_state = COMPRESS;
        end
      end


      COMPRESS: begin
        word_index_next  = 7'd0;
        round_index_next = round_index + 1;
        if (round_index == 7'd64 - 1) begin  // About to complete all 64 rounds
          next_state = UPDATE_HASH;
        end
      end

      UPDATE_HASH: begin
        word_index_next  = 7'd0;
        round_index_next = 6'd0;
        chunk_idx_next   = chunk_idx + 1;
        if (!is_last_chunk && chunk_idx < MAX_CHUNKS - 1) begin
          next_state = GENERATE_WORDS;
          mem_addr   = message_addr + {9'h0, word_index_next} + chunk_idx_next * 16;
        end else begin
          next_state = WRITE_OUTPUT;
        end
      end

      WRITE_OUTPUT: begin
        word_index_next = word_index + 1;
        mem_we          = 1'b1;
        mem_addr        = output_addr + {9'h0, word_index};
        mem_write_data  = hash[word_index[2:0]];  // Only use lower 3 bits for hash array
        if (word_index == 7'd7) begin  // Written all 8 hash words
          next_state = DONE;
        end
      end

      DONE: begin
        done             = 1'b1;
        word_index_next  = 7'd0;
        round_index_next = 6'd0;
        chunk_idx_next   = 0;
        if (!start) begin  // Wait for start to be deasserted
          next_state = IDLE;
        end
      end

      default: begin
        next_state = IDLE;
      end
    endcase
  end

  // Sequential logic
  always_ff @(posedge clk or negedge rst_n) begin
    if (~rst_n) begin
      cur_state     <= IDLE;
      word_index    <= 7'h0;
      round_index   <= 6'h0;
      chunk_idx     <= '0;
      is_last_chunk <= 1'b0;
      // Initialize hash values
      for (int i = 0; i < 8; i++) begin
        hash[i] <= H_INIT[i];
      end

      // Initialize working variables
      {a, b, c, d, e, f, g, h} <= {
        H_INIT[0], H_INIT[1], H_INIT[2], H_INIT[3], H_INIT[4], H_INIT[5], H_INIT[6], H_INIT[7]
      };

      // Clear message schedule
      for (int i = 0; i < 64; i++) begin
        w[i] <= 32'h0;
      end

    end else begin
      cur_state   <= next_state;
      word_index  <= word_index_next;
      round_index <= round_index_next;
      chunk_idx   <= chunk_idx_next;
      case (cur_state)
        IDLE: begin
          if (start) begin
            word_index    <= 7'h0;
            round_index   <= 6'h0;
            chunk_idx     <= 0;
            is_last_chunk <= 1'b0;
            // Reset hash values for new computation
            for (int i = 0; i < 8; i++) begin
              hash[i] <= H_INIT[i];
            end
            {a, b, c, d, e, f, g, h} <= {
              H_INIT[0], H_INIT[1], H_INIT[2], H_INIT[3], H_INIT[4], H_INIT[5], H_INIT[6], H_INIT[7]
            };
          end
        end

        GENERATE_WORDS: begin
          if (word_index < 16) begin  // Read 16 words for 512-bit block
            w[word_index[5:0]] <= mem_read_data;

            // Check if this is the last chunk by looking for SHA-256 padding pattern
            // The padding always starts with 0x80 byte, often appearing as 0x80000000
            if (!is_last_chunk) begin
              // Look for common padding patterns
              if ((mem_read_data == 32'h80000000) ||  // Most common: 0x80 at start of word
                  ((mem_read_data & 32'hFF000000) == 32'h80000000) ||
                      ((mem_read_data & 32'h00FF0000) == 32'h00800000) ||
                      ((mem_read_data & 32'h0000FF00) == 32'h00008000) ||
                      ((mem_read_data & 32'h000000FF) == 32'h00000080)) begin
                is_last_chunk <= 1'b1;
              end
            end
          end else begin
            w[word_index] <= w[word_index-16] + gamma0(
                w[word_index-15]
            ) + w[word_index-7] + gamma1(
                w[word_index-2]
            );
            if (word_index == 7'd64 - 1) begin
              {a, b, c, d, e, f, g, h} <= {
                hash[0], hash[1], hash[2], hash[3], hash[4], hash[5], hash[6], hash[7]
              };
            end
          end
        end
        COMPRESS: begin
          // Perform one round of SHA-256 compression
          // temp1_comb and temp2_comb are computed combinationally above

          // Always update working variables for each round (including round 63)
          h <= g;
          g <= f;
          f <= e;
          e <= d + temp1_comb;
          d <= c;
          c <= b;
          b <= a;
          a <= temp1_comb + temp2_comb;
        end

        UPDATE_HASH: begin
          // Add the final working variables to the hash values
          // The working variables now contain the results after all 64 rounds
          hash[0] <= hash[0] + a;
          hash[1] <= hash[1] + b;
          hash[2] <= hash[2] + c;
          hash[3] <= hash[3] + d;
          hash[4] <= hash[4] + e;
          hash[5] <= hash[5] + f;
          hash[6] <= hash[6] + g;
          hash[7] <= hash[7] + h;

          if (!is_last_chunk && chunk_idx < MAX_CHUNKS - 1) begin
            for (int i = 0; i < 64; i++) begin
              w[i] <= 32'h0;  // Clear message schedule for next chunk
            end
          end
        end


        // WRITE_OUTPUT: begin
        // end

        // DONE: begin
        //   // Stay in DONE state until start is deasserted
        // end

        default: begin
          // Default case - should not happen
        end
      endcase
    end
  end

endmodule
