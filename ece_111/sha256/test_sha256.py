#!/usr/bin/env python3

import cocotb
from cocotb.triggers import <PERSON><PERSON><PERSON>, FallingE<PERSON>, Timer, <PERSON>Only, NextTimeStep
from cocotb.clock import Clock
from cocotb.binary import BinaryValue
import os
import random
import struct
import hashlib
from pathlib import Path
from cocotb.runner import get_runner

# Import our reference SHA-256 implementation
import sys

sys.path.append(str(Path(__file__).parent))
from sha256_ref import SHA256


class SHA256TestHelper:
    """Helper class for SHA-256 testing"""

    def __init__(self, dut):
        self.dut = dut
        self.sha256_ref = SHA256()

    def get_memory_value(self, addr):
        """Helper function to read memory value"""
        return int(self.dut.mem_inst.mem_data[addr].value)

    def set_memory_value(self, addr, value):
        """Helper function to set memory value"""
        self.dut.mem_inst.mem_data[addr].value = value

    async def setup_memory_data(self, start_addr, data_list):
        """Setup initial memory data for testing"""
        for i, data in enumerate(data_list):
            self.set_memory_value(start_addr + i, data)
        # Wait a cycle for memory to settle
        await RisingEdge(self.dut.clk)

    async def verify_memory_data(self, start_addr, expected_data_list):
        """Verify memory data matches expected values"""
        for i, expected in enumerate(expected_data_list):
            actual = self.get_memory_value(start_addr + i)
            assert (
                actual == expected
            ), f"Memory mismatch at addr {start_addr + i}: expected 0x{expected:08x}, got 0x{actual:08x}"

    def prepare_padded_message(self, message):
        """Prepare a padded message for SHA-256 (support 1 or 2 blocks based on message length)"""
        if isinstance(message, str):
            message = message.encode("utf-8")

        # Original message length in bits
        original_len_bits = len(message) * 8

        # Append '1' bit (0x80) then pad zeros to reach 56 bytes mod 64
        message += b"\x80"
        while len(message) % 64 != 56:
            message += b"\x00"

        # Append original length as 64-bit big-endian
        message += struct.pack(">Q", original_len_bits)

        # Convert to 32-bit words (big-endian)
        words = []
        for i in range(0, len(message), 4):
            words.append(struct.unpack(">I", message[i : i + 4])[0])

        # Determine how many blocks we need
        num_blocks = (len(words) + 15) // 16  # Round up to nearest block
        target_words = num_blocks * 16
        
        # Pad to target number of words
        while len(words) < target_words:
            words.append(0)

        return words[:target_words]

    def compute_reference_hash(self, message):
        """Compute reference SHA-256 hash"""
        return self.sha256_ref.digest(message)

    def hash_to_words(self, hash_bytes):
        """Convert hash bytes to 32-bit words"""
        words = []
        for i in range(0, len(hash_bytes), 4):
            word = struct.unpack(">I", hash_bytes[i : i + 4])[0]
            words.append(word)
        return words


async def reset_dut(dut):
    """Reset the DUT"""
    dut.rst_n.value = 0
    dut.start.value = 0
    dut.message_addr.value = 0
    dut.output_addr.value = 0
    await RisingEdge(dut.clk)
    await RisingEdge(dut.clk)
    dut.rst_n.value = 1
    await RisingEdge(dut.clk)


async def start_sha256(dut, message_addr, output_addr):
    """Start SHA-256 computation"""
    dut.message_addr.value = message_addr
    dut.output_addr.value = output_addr
    dut.start.value = 1
    await RisingEdge(dut.clk)
    dut.start.value = 0


async def wait_for_done(dut, timeout_cycles=100000):
    """Wait for computation to complete"""
    cycles = 0
    while not dut.done.value and cycles < timeout_cycles:
        await RisingEdge(dut.clk)
        cycles += 1

    if cycles >= timeout_cycles:
        raise Exception(
            f"Timeout waiting for done signal after {timeout_cycles} cycles"
        )

    return cycles


@cocotb.test()
async def test_sha256_basic(dut):
    """Test basic SHA-256 functionality"""

    dut._log.info("Starting basic SHA-256 test")

    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())

    # Create test helper
    helper = SHA256TestHelper(dut)

    # Reset
    await reset_dut(dut)

    # Test with simple message "abc"
    test_message = "abc"
    dut._log.info(f"Testing message: '{test_message}'")

    # Prepare padded message
    padded_words = helper.prepare_padded_message(test_message)
    dut._log.info(f"Padded message: {[hex(w) for w in padded_words]}")

    # Setup memory with padded message
    message_addr = 0x100
    output_addr = 0x200
    await helper.setup_memory_data(message_addr, padded_words)

    # Start computation
    await start_sha256(dut, message_addr, output_addr)

    # Wait for completion
    cycles = await wait_for_done(dut)
    dut._log.info(f"Computation completed in {cycles} cycles")

    # Read result from memory
    result_words = []
    for i in range(8):
        word = helper.get_memory_value(output_addr + i)
        result_words.append(word)

    # Convert to bytes
    result_bytes = b"".join(struct.pack(">I", word) for word in result_words)
    result_hex = result_bytes.hex()

    # Compute reference
    reference_hash = helper.compute_reference_hash(test_message)
    reference_hex = reference_hash.hex()

    dut._log.info(f"Hardware result: {result_hex}")
    dut._log.info(f"Reference result: {reference_hex}")

    assert (
        result_hex == reference_hex
    ), f"Hash mismatch: expected {reference_hex}, got {result_hex}"
    dut._log.info("✓ Basic test passed!")


@cocotb.test()
async def test_sha256_empty_string(dut):
    """Test SHA-256 with empty string"""

    dut._log.info("Starting empty string SHA-256 test")

    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())

    # Create test helper
    helper = SHA256TestHelper(dut)

    # Reset
    await reset_dut(dut)

    # Test with empty message
    test_message = ""
    dut._log.info(f"Testing empty message")

    # Prepare padded message
    padded_words = helper.prepare_padded_message(test_message)

    # Setup memory with padded message
    message_addr = 0x300
    output_addr = 0x400
    await helper.setup_memory_data(message_addr, padded_words)

    # Start computation
    await start_sha256(dut, message_addr, output_addr)

    # Wait for completion
    cycles = await wait_for_done(dut)
    dut._log.info(f"Computation completed in {cycles} cycles")

    # Read and verify result
    result_words = []
    for i in range(8):
        word = helper.get_memory_value(output_addr + i)
        result_words.append(word)

    result_bytes = b"".join(struct.pack(">I", word) for word in result_words)
    result_hex = result_bytes.hex()

    reference_hash = helper.compute_reference_hash(test_message)
    reference_hex = reference_hash.hex()

    dut._log.info(f"Hardware result: {result_hex}")
    dut._log.info(f"Reference result: {reference_hex}")

    assert (
        result_hex == reference_hex
    ), f"Hash mismatch: expected {reference_hex}, got {result_hex}"
    dut._log.info("✓ Empty string test passed!")


@cocotb.test()
async def test_sha256_multiple_messages(dut):
    """Test SHA-256 with multiple different messages"""

    dut._log.info("Starting multiple messages SHA-256 test")

    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())

    # Create test helper
    helper = SHA256TestHelper(dut)

    # Reset
    await reset_dut(dut)

    # Test messages
    test_messages = [
        "a",
        "hello",
        "The quick brown fox",
        "1234567890",
        "SHA-256 test",
        "1234567890" * 16,
        "1234567890" * 100,
    ]

    for i, test_message in enumerate(test_messages):
        dut._log.info(f"Testing message {i+1}: '{test_message}'")

        # Prepare padded message
        padded_words = helper.prepare_padded_message(test_message)
        dut._log.info(f"Padded message: {[hex(w) for w in padded_words]}")

        # Setup memory with padded message
        message_addr = 0x500 + i * 0x100
        output_addr = 0x600 + i * 0x100
        await helper.setup_memory_data(message_addr, padded_words)

        # Start computation
        await start_sha256(dut, message_addr, output_addr)

        # Wait for completion
        cycles = await wait_for_done(dut)
        dut._log.info(f"Message {i+1} completed in {cycles} cycles")

        # Read and verify result
        result_words = []
        for j in range(8):
            word = helper.get_memory_value(output_addr + j)
            result_words.append(word)

        result_bytes = b"".join(struct.pack(">I", word) for word in result_words)
        result_hex = result_bytes.hex()

        reference_hash = helper.compute_reference_hash(test_message)
        reference_hex = reference_hash.hex()

        dut._log.info(f"Message {i+1} - Hardware: {result_hex}")
        dut._log.info(f"Message {i+1} - Reference: {reference_hex}")

        assert (
            result_hex == reference_hex
        ), f"Hash mismatch for message {i+1}: expected {reference_hex}, got {result_hex}"

    dut._log.info("✓ Multiple messages test passed!")


@cocotb.test()
async def test_sha256_back_to_back(dut):
    """Test back-to-back stimulus where new input can be applied on same cycle as done"""

    dut._log.info("Starting back-to-back stimulus SHA-256 test")

    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())

    # Create test helper
    helper = SHA256TestHelper(dut)

    # Reset
    await reset_dut(dut)

    # Prepare multiple test messages
    test_messages = ["test1", "test2", "test3"]
    message_addrs = [0x1000, 0x1100, 0x1200]
    output_addrs = [0x2000, 0x2100, 0x2200]

    # Setup all messages in memory
    for i, message in enumerate(test_messages):
        padded_words = helper.prepare_padded_message(message)
        await helper.setup_memory_data(message_addrs[i], padded_words)

    # Perform back-to-back operations
    for i in range(len(test_messages)):
        dut._log.info(f"Starting computation {i+1} for message: '{test_messages[i]}'")

        # Start computation
        dut.message_addr.value = message_addrs[i]
        dut.output_addr.value = output_addrs[i]
        dut.start.value = 1
        await RisingEdge(dut.clk)
        dut.start.value = 0

        # Wait for done signal
        cycles = 0
        while not dut.done.value and cycles < 1000:
            await RisingEdge(dut.clk)
            cycles += 1

        if cycles >= 1000:
            raise Exception(f"Timeout waiting for done signal for message {i+1}")

        dut._log.info(f"Computation {i+1} completed in {cycles} cycles")

        # On the same cycle that done is asserted, we can start the next computation
        # This tests the FSM transition from DONE to processing state without idle cycles
        if i < len(test_messages) - 1:
            # Apply next stimulus on the same cycle as done
            await NextTimeStep()  # Small delay to ensure done signal is stable
            dut.message_addr.value = message_addrs[i + 1]
            dut.output_addr.value = output_addrs[i + 1]
            dut.start.value = 1
            await RisingEdge(dut.clk)
            dut.start.value = 0

            # Wait for done to go low (indicating new computation started)
            timeout = 10
            while dut.done.value and timeout > 0:
                await RisingEdge(dut.clk)
                timeout -= 1

            if timeout == 0:
                raise Exception(
                    f"Done signal did not go low for back-to-back transition {i+1}"
                )

            dut._log.info(f"✓ Back-to-back transition {i+1} successful")

        # Verify the result
        result_words = []
        for j in range(8):
            word = helper.get_memory_value(output_addrs[i] + j)
            result_words.append(word)

        result_bytes = b"".join(struct.pack(">I", word) for word in result_words)
        result_hex = result_bytes.hex()

        reference_hash = helper.compute_reference_hash(test_messages[i])
        reference_hex = reference_hash.hex()

        assert (
            result_hex == reference_hex
        ), f"Hash mismatch for message {i+1}: expected {reference_hex}, got {result_hex}"
        dut._log.info(f"✓ Message {i+1} hash verification passed")

    dut._log.info("✓ Back-to-back stimulus test passed!")


@cocotb.test()
async def test_sha256_stress(dut):
    """Stress test with rapid back-to-back operations"""

    dut._log.info("Starting stress test with rapid operations")

    # Start clock
    clock = Clock(dut.clk, 10, units="ns")
    cocotb.start_soon(clock.start())

    # Create test helper
    helper = SHA256TestHelper(dut)

    # Reset
    await reset_dut(dut)

    # Generate random test messages
    num_tests = 5
    test_messages = [f"stress_test_{i}" for i in range(num_tests)]

    for i, message in enumerate(test_messages):
        dut._log.info(f"Stress test {i+1}/{num_tests}: '{message}'")

        # Prepare message
        padded_words = helper.prepare_padded_message(message)
        message_addr = 0x3000 + i * 0x50
        output_addr = 0x4000 + i * 0x50

        # Setup memory
        await helper.setup_memory_data(message_addr, padded_words)

        # Start computation
        await start_sha256(dut, message_addr, output_addr)

        # Wait for completion
        cycles = await wait_for_done(dut)
        dut._log.info(f"Stress test {i+1} completed in {cycles} cycles")

        # Verify result
        result_words = []
        for j in range(8):
            word = helper.get_memory_value(output_addr + j)
            result_words.append(word)

        result_bytes = b"".join(struct.pack(">I", word) for word in result_words)
        result_hex = result_bytes.hex()

        reference_hash = helper.compute_reference_hash(message)
        reference_hex = reference_hash.hex()

        assert (
            result_hex == reference_hex
        ), f"Stress test {i+1} hash mismatch: expected {reference_hex}, got {result_hex}"

    dut._log.info("✓ Stress test passed!")


def test_run():
    """Run the test suite"""
    tests_dir = Path(__file__).parent
    print(f"Running tests from: {tests_dir}")
    hdl_dir = tests_dir
    sim = os.getenv("SIM", "icarus")
    runner = get_runner(sim)

    # Build the simulation
    runner.build(
        verilog_sources=[
            hdl_dir / "sha256_top.sv",
            hdl_dir / "sha256.sv",
            hdl_dir / "mem.sv",
        ],
        hdl_toplevel="sha256_top",
        parameters={"ADDR_WIDTH": "16", "DATA_WIDTH": "32"},
        waves=True,
        build_dir=f"sim_build/sha256",
    )

    # Run the tests
    runner.test(
        test_module="test_sha256",
        hdl_toplevel="sha256_top",
        parameters={"ADDR_WIDTH": "16", "DATA_WIDTH": "32"},
        extra_env={},
    )


if __name__ == "__main__":
    test_run()
